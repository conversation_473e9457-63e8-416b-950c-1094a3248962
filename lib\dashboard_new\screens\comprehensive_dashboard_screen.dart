import 'package:flutter/material.dart';
import 'package:flutter_application_2/screens/widgets/app_drawer.dart';
import 'package:get/get.dart';
import '../controllers/dashboard_controller.dart';
import '../models/dashboard_models.dart';

import '../widgets/dashboard_layout_manager.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../constants/app_bar_styles.dart';
import '../../services/api/activity_logs_api_service.dart';
import '../../models/activity_log_models.dart';
import '../../models/task_models.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/theme_controller.dart';
import '../../controllers/notifications_controller.dart';
import '../../controllers/admin_controller.dart';
import '../../controllers/department_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/subtasks_controller.dart';
import '../../controllers/attachments_controller.dart';
import '../../controllers/task_documents_controller.dart';
import '../../utils/image_helper.dart';
import '../../services/unified_permission_service.dart';
import '../../services/cache_service.dart';
import '../../screens/widgets/search/search_app_bar_action.dart';
import '../../widgets/notifications_button.dart';


/// شاشة لوحة التحكم الشاملة والاحترافية
class ComprehensiveDashboardScreen extends StatefulWidget {
  const ComprehensiveDashboardScreen({super.key});

  @override
  State<ComprehensiveDashboardScreen> createState() => _ComprehensiveDashboardScreenState();
}

class _ComprehensiveDashboardScreenState extends State<ComprehensiveDashboardScreen>
    with TickerProviderStateMixin {
  late final DashboardController _controller;
  late final TabController _tabController;
  late final UnifiedPermissionService _permissionService;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<DashboardController>();
    _tabController = TabController(length: 3, vsync: this);
    _permissionService = Get.find<UnifiedPermissionService>();

    // تحميل البيانات مباشرة - سيتم فحص الصلاحيات داخل loadDashboardData
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.loadDashboardData(forceRefresh: true);
      _loadNotifications();
    });

    // تحميل الإشعارات فوراً أيضاً
    _loadNotifications();
  }

  /// تحميل الإشعارات للمستخدم الحالي
  Future<void> _loadNotifications() async {
    try {
      final authController = Get.find<AuthController>();

      // التحقق من وجود NotificationsController
      if (!Get.isRegistered<NotificationsController>()) {
        debugPrint('⚠️ NotificationsController غير مسجل، سيتم تسجيله الآن');
        Get.put(NotificationsController(), permanent: true);
      }

      final notificationsController = Get.find<NotificationsController>();

      if (authController.currentUser.value != null) {
        // تحميل جميع الإشعارات
        await notificationsController.loadAllNotifications();
        // تحميل الإشعارات غير المقروءة
        await notificationsController.loadUnreadNotifications();

        debugPrint('✅ تم تحميل الإشعارات في لوحة التحكم الشاملة - عدد غير المقروءة: ${notificationsController.unreadCount}');
      } else {
        debugPrint('⚠️ لا يوجد مستخدم مسجل دخول لتحميل الإشعارات');
      }
    } catch (e) {
      debugPrint('⚠️ خطأ في تحميل الإشعارات: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// إعادة تحميل البيانات أو إعادة تشغيل التطبيق
  Future<void> _reloadData() async {
    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // إعادة تحميل بيانات لوحة التحكم
      await _controller.loadDashboardData(forceRefresh: true);

      // إعادة تحميل المهام والبيانات المرتبطة بها
      try {
        if (Get.isRegistered<TaskController>()) {
          final taskController = Get.find<TaskController>();
          await taskController.loadAllTasks();
          debugPrint('✅ تم تحديث المهام');
        } else {
          debugPrint('⚠️ TaskController غير مسجل');
        }
      } catch (e) {
        debugPrint('⚠️ خطأ في تحديث المهام: $e');
      }

      // إعادة تحميل المهام الفرعية
      try {
        if (Get.isRegistered<SubtasksController>()) {
          final subtasksController = Get.find<SubtasksController>();
          await subtasksController.refresh();
          debugPrint('✅ تم تحديث المهام الفرعية');
        }
      } catch (e) {
        debugPrint('⚠️ خطأ في تحديث المهام الفرعية: $e');
      }

      // إعادة تحميل المرفقات
      try {
        if (Get.isRegistered<AttachmentsController>()) {
          final attachmentsController = Get.find<AttachmentsController>();
          await attachmentsController.refresh();
          debugPrint('✅ تم تحديث المرفقات');
        }
      } catch (e) {
        debugPrint('⚠️ خطأ في تحديث المرفقات: $e');
      }

      // إعادة تحميل مستندات المهام
      try {
        if (Get.isRegistered<TaskDocumentsController>()) {
          final taskDocumentsController = Get.find<TaskDocumentsController>();
          await taskDocumentsController.refresh();
          debugPrint('✅ تم تحديث مستندات المهام');
        }
      } catch (e) {
        debugPrint('⚠️ خطأ في تحديث مستندات المهام: $e');
      }

      // إعادة تحميل الإشعارات
      try {
        final authController = Get.find<AuthController>();

        // التحقق من وجود NotificationsController
        if (!Get.isRegistered<NotificationsController>()) {
          debugPrint('⚠️ NotificationsController غير مسجل، سيتم تسجيله الآن');
          Get.put(NotificationsController(), permanent: true);
        }

        final notificationsController = Get.find<NotificationsController>();
        if (authController.currentUser.value != null) {
          // تحديث الإشعارات
          await notificationsController.loadAllNotifications();
          await notificationsController.loadUnreadNotifications();
          debugPrint('✅ تم تحديث الإشعارات - عدد غير المقروءة: ${notificationsController.unreadCount}');
        }
      } catch (e) {
        debugPrint('⚠️ خطأ في تحديث الإشعارات: $e');
      }

      // إعادة تحميل البيانات الإدارية (المستخدمين، الأدوار، الصلاحيات، الأقسام)
      try {
        final adminController = Get.find<AdminController>();
        await adminController.refreshAllData();
        debugPrint('✅ تم تحديث البيانات الإدارية');
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على AdminController أو خطأ في التحديث: $e');
      }

      // إعادة تحميل بيانات الأقسام
      try {
        final departmentController = Get.find<DepartmentController>();
        await departmentController.loadAllDepartments();
        debugPrint('✅ تم تحديث بيانات الأقسام');
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على DepartmentController أو خطأ في التحديث: $e');
      }

      // إعادة تحميل بيانات المستخدمين
      try {
        final userController = Get.find<UserController>();
        await userController.loadAllUsers();
        debugPrint('✅ تم تحديث بيانات المستخدمين');
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على UserController أو خطأ في التحديث: $e');
      }

      // إعادة تحميل صلاحيات المستخدم الحالي
      try {
        final permissionService = Get.find<UnifiedPermissionService>();
        await permissionService.refreshCurrentUserPermissions();
        debugPrint('✅ تم تحديث صلاحيات المستخدم');
      } catch (e) {
        debugPrint('⚠️ خطأ في تحديث الصلاحيات: $e');
      }

      // مسح التخزين المؤقت لضمان تحديث البيانات
      try {
        final cacheService = Get.find<CacheService>();
        await cacheService.clear();
        debugPrint('✅ تم مسح التخزين المؤقت');
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على CacheService أو خطأ في المسح: $e');
      }

      // إغلاق مؤشر التحميل
      Get.back();

      // إظهار رسالة نجاح
      Get.snackbar(
        'تم التحديث',
        'تم تحديث جميع البيانات بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.white,
        duration: const Duration(seconds: 3),
      );

      debugPrint('✅ تم إعادة تحميل جميع البيانات بنجاح');
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      Get.back();

      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ في التحديث',
        'حدث خطأ أثناء تحديث البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
        duration: const Duration(seconds: 5),
      );

      debugPrint('❌ خطأ في إعادة تحميل البيانات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: _buildAppBar(),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          _buildTabBar(),
         
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildChartsTab(),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'لوحة التحكم الشاملة',
        style: AppStyles.titleLarge.copyWith(
          color: Theme.of(context).appBarTheme.foregroundColor,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
      foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      elevation: 2,
      shadowColor: AppColors.getShadowColor(0.1),
      actions: [
        // زر البحث الشامل
        if (_permissionService.canAccessSearch())
          const SearchAppBarAction(),

        // زر الإشعارات
        if (_permissionService.canViewNotifications())
          NotificationsButton(iconColor: Theme.of(context).appBarTheme.foregroundColor),

        // زر إعادة تحميل البيانات
        if (_permissionService.canRefreshAdminData())
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Theme.of(context).appBarTheme.foregroundColor,
            ),
            tooltip: 'إعادة تحميل البيانات',
            onPressed: _reloadData,
          ),

        // زر تبديل السمة (تغيير مباشر)
        if (_permissionService.canChangeSystemTheme())
          Obx(() {
            final themeController = Get.find<ThemeController>();
            return AppBarStyles.appBarIconButton(
              icon: themeController.isDarkModeRx.value
                  ? Icons.light_mode
                  : Icons.dark_mode,
              tooltip: themeController.isDarkModeRx.value
                  ? 'الوضع الفاتح'.tr
                  : 'الوضع الداكن'.tr,
              onPressed: () async {
                // حفظ ألوان السمة قبل التغيير
                final cardColor = Theme.of(context).cardColor;
                final textColor = Theme.of(context).textTheme.bodyMedium?.color;

                // تبديل السمة مباشرة
                await themeController.toggleTheme();
                // إظهار رسالة تأكيد
                Get.snackbar(
                  'تم تغيير السمة',
                  themeController.isDarkModeRx.value
                      ? 'تم تفعيل الوضع الداكن'
                      : 'تم تفعيل الوضع الفاتح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: cardColor,
                  colorText: textColor,
                  duration: const Duration(seconds: 2),
                );
              },
            );
          }),

        // زر المساعدة
        if (_permissionService.canAccessHelpSystem())
          IconButton(
            icon: Icon(
              Icons.help_outline,
              color: Theme.of(context).appBarTheme.foregroundColor,
            ),
            tooltip: 'help'.tr,
            onPressed: () {
              Get.dialog(AlertDialog(
                backgroundColor: Theme.of(context).cardColor,
                title: Text(
                  'هذه الميزة قيد التطوير',
                  style: AppStyles.titleMedium.copyWith(
                    color: Theme.of(context).textTheme.titleMedium?.color,
                  ),
                ),
                content: Text(
                  'قيد التطوير',
                  style: AppStyles.bodyMedium.copyWith(
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Get.back(),
                    style: AppStyles.textButtonStyle,
                    child: Text(
                      'close'.tr,
                      style: AppStyles.bodyMedium.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ],
              ));
            },
          ),

        // معلومات المستخدم الحالي مع تصميم محسن
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Obx(() {
            final authController = Get.find<AuthController>();
            final user = authController.currentUser.value;
            if (user == null) return const SizedBox.shrink();

            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اسم المستخدم
                Text(
                  user.name.length > 15 ? '${user.name.substring(0, 15)}...' : user.name,
                  style: AppStyles.titleMedium.copyWith(
                    color: Theme.of(context).appBarTheme.foregroundColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 12),
                // صورة المستخدم المحسنة
                ImageHelper.buildProfileImage(
                  imagePath: user.profileImage,
                  radius: 18,
                  fallbackText: user.name,
                  backgroundColor: AppColors.white,
                  textColor: AppColors.primary,
                ),
              ],
            );
          }),
        ),
      ],
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).appBarTheme.backgroundColor,
      child: TabBar(
        controller: _tabController,
        indicatorColor: Theme.of(context).appBarTheme.foregroundColor,
        indicatorWeight: 3,
        labelColor: Theme.of(context).appBarTheme.foregroundColor,
        unselectedLabelColor: Theme.of(context).appBarTheme.foregroundColor?.withValues(alpha: 0.7),
        labelStyle: AppStyles.titleSmall.copyWith(fontWeight: FontWeight.w600),
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'نظرة عامة',
          ),
          Tab(
            icon: Icon(Icons.bar_chart),
            text: 'المخططات',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: 'التحليلات',
          ),
        ],
      ),
    );
  }

  // /// بناء قسم المرشحات
  // Widget _buildFiltersSection() {
  //   return Container(
  //     margin: const EdgeInsets.all(16),
  //     child: DashboardFilterWidget(
  //       onFiltersChanged: (filters) {
  //         // معالجة تغيير المرشحات
  //       },
  //     ),
  //   );
  // }

  /// بناء تبويب النظرة العامة المحسن
  ///
  /// ملاحظة مهمة: جميع البيانات المعروضة في هذا التبويب حقيقية ومستمدة من قاعدة البيانات
  /// لا توجد أي بيانات تجريبية أو وهمية أو افتراضية
  Widget _buildOverviewTab() {
    return Obx(() {
      if (_controller.isLoading) {
         return _buildLoadingState();
      }

      if (_controller.error.isNotEmpty) {
        return _buildErrorState();
      }

      return RefreshIndicator(
        onRefresh: _controller.refreshDashboard,  
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقة ترحيب المستخدم
              _buildWelcomeCard(),
              const SizedBox(height: 16),
              // مؤشرات سريعة في الأعلى - معلومات أساسية مضغوطة
              if (_permissionService.canViewStatistics()) _buildQuickIndicators(),
              if (_permissionService.canViewStatistics()) const SizedBox(height: 16),
              // الإحصائيات التفصيلية - بيانات حقيقية من API
              if (_permissionService.canViewStatistics()) _buildCompactStatisticsGrid(),
              if (_permissionService.canViewStatistics()) const SizedBox(height: 16),
              // إجراءات سريعة للتنقل
              if (_permissionService.canCreateTask() || _permissionService.canAccessReports() ||
                  _permissionService.canAccessChat() || _permissionService.canAccessSearch())
                _buildQuickActionsRow(),
              const SizedBox(height: 16),
              // إحصائيات متقدمة وتحليلات ذكية
              if (_permissionService.canViewAdvancedStatistics()) _buildAdvancedInsights(),
              if (_permissionService.canViewAdvancedStatistics()) const SizedBox(height: 16),
              // مؤشرات صحة النظام المحسوبة من البيانات الحقيقية
              if (_permissionService.canViewAdvancedStatistics()) _buildSystemHealthIndicators(),
          

              const SizedBox(height: 16),
              // النشاط الأخير والسجلات - بيانات حقيقية من APIs
              if (_permissionService.canViewStatistics()) _buildRecentActivitySection(),
            ],
          ),
        ),
      );
    });
  }

  /// بناء بطاقة ترحيب المستخدم
  Widget _buildWelcomeCard() {
    return Obx(() {
      final authController = Get.find<AuthController>();
      final user = authController.currentUser.value;
      if (user == null) return const SizedBox.shrink();

      // تحديد التحية حسب الوقت
      final hour = DateTime.now().hour;
      String greeting;
      IconData greetingIcon;

      if (hour < 12) {
        greeting = 'صباح الخير';
        greetingIcon = Icons.wb_sunny;
      } else if (hour < 17) {
        greeting = 'مساء الخير';
        greetingIcon = Icons.wb_sunny_outlined;
      } else {
        greeting = 'مساء الخير';
        greetingIcon = Icons.nights_stay;
      }

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary,
              AppColors.primary.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // صورة المستخدم الكبيرة
            ImageHelper.buildProfileImage(
              imagePath: user.profileImage,
              radius: 35,
              fallbackText: user.name,
              backgroundColor: AppColors.white,
              textColor: AppColors.primary,
            ),
            const SizedBox(width: 16),
            // معلومات الترحيب
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        greetingIcon,
                        color: AppColors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        greeting,
                        style: AppStyles.bodyMedium.copyWith(
                          color: AppColors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.name,
                    style: AppStyles.titleLarge.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    user.roleName.isNotEmpty ? user.roleName : 'مستخدم النظام',
                    style: AppStyles.bodySmall.copyWith(
                      color: AppColors.white.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
            // أيقونة لوحة التحكم
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.dashboard,
                color: AppColors.white,
                size: 24,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء المؤشرات السريعة
  /// شريط علوي يعرض أهم المؤشرات بشكل مضغوط
  Widget _buildQuickIndicators() {
    // فحص الصلاحيات للبيانات الحساسة
    if (!_permissionService.canViewStatistics()) {
      return const SizedBox.shrink();
    }

    final taskStats = _controller.taskStatistics;
    final userStats = _controller.userStatistics;

    // حساب المؤشرات الرئيسية
    final completionRate = taskStats.totalTasks > 0
        ? (taskStats.completedTasks / taskStats.totalTasks * 100)
        : 0.0;
    final activityRate = userStats.totalUsers > 0 && _permissionService.canViewAdvancedStatistics()
        ? (userStats.activeUsers / userStats.totalUsers * 100)
        : 0.0;
    final urgentTasks = taskStats.overdueTasks;
    final systemHealth = _calculateSystemHealth(taskStats, userStats);

    return Container(
      padding: const EdgeInsets.all(5),
      // margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // مؤشر الإنجاز
          Expanded(
            child: _buildQuickIndicatorItem(
              'معدل الإنجاز',
              '${completionRate.toStringAsFixed(0)}%',
              Icons.check_circle_outline,
              _getPerformanceColor(completionRate),
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border,
          ),
          // مؤشر النشاط - يظهر فقط إذا كان لديه صلاحية عرض المستخدمين
          if (_permissionService.canViewAdvancedStatistics())
            Expanded(
              child: _buildQuickIndicatorItem(
                'نشاط المستخدمين',
                '${activityRate.toStringAsFixed(0)}%',
                Icons.people_outline,
                _getPerformanceColor(activityRate),
              ),
            ),
          if (_permissionService.canViewAdvancedStatistics())
            Container(
              width: 1,
              height: 40,
              color: AppColors.border,
            ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border,
          ),
          // المهام المتأخرة
          Expanded(
            child: _buildQuickIndicatorItem(
              'مهام متأخرة',
              urgentTasks.toString(),
              Icons.warning_amber_outlined,
              urgentTasks > 0 ? AppColors.error : AppColors.success,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border,
          ),
          // صحة النظام
          Expanded(
            child: _buildQuickIndicatorItem(
              'صحة النظام',
              _getHealthStatus(systemHealth),
              Icons.health_and_safety_outlined,
              _getHealthColor(systemHealth),
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border,
          ),
          // المهام المستحقة اليوم
          Expanded(
            child: FutureBuilder<int>(
              future: _getTasksDueToday(),
              builder: (context, snapshot) {
                final count = snapshot.data ?? 0;
                return _buildQuickIndicatorItem(
                  'مستحقة اليوم',
                  count.toString(),
                  Icons.today_outlined,
                  count > 0 ? AppColors.warning : AppColors.success,
                );
              },
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border,
          ),
          // المهام المستحقة غداً
          Expanded(
            child: FutureBuilder<int>(
              future: _getTasksDueTomorrow(),
              builder: (context, snapshot) {
                final count = snapshot.data ?? 0;
                return _buildQuickIndicatorItem(
                  'مستحقة غداً',
                  count.toString(),
                  Icons.schedule_outlined,
                  count > 0 ? AppColors.info : AppColors.success,
                );
              },
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border,
          ),
          // المهام المستحقة بعد يومين
          Expanded(
            child: FutureBuilder<int>(
              future: _getTasksDueAfterTomorrow(),
              builder: (context, snapshot) {
                final count = snapshot.data ?? 0;
                return _buildQuickIndicatorItem(
                  'بعد يومين',
                  count.toString(),
                  Icons.event_outlined,
                  count > 0 ? AppColors.primary : AppColors.success,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر مؤشر سريع
  Widget _buildQuickIndicatorItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: AppStyles.bodySmall.copyWith(
            fontSize: 10,
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// بناء تبويب المخططات
  Widget _buildChartsTab() {
    return Obx(() {
      if (_controller.isLoading) {
        return _buildLoadingState();
      }

      if (_controller.error.isNotEmpty) {
        return _buildErrorState();
      }

      return RefreshIndicator(
        onRefresh: _controller.refreshDashboard,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: _buildChartsGrid(),
        ),
      );
    });
  }

  /// بناء تبويب التحليلات
  Widget _buildAnalyticsTab() {
    return Obx(() {
      if (_controller.isLoading) {
        return _buildLoadingState();
      }

      if (_controller.error.isNotEmpty) {
        return _buildErrorState();
      }

      return RefreshIndicator(
        onRefresh: _controller.refreshDashboard,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // القسم الأول: نظرة عامة متقدمة
              _buildAnalyticsHeader('نظرة عامة متقدمة', Icons.analytics, AppColors.primary),
              const SizedBox(height: 16),
              if (_permissionService.canViewAdvancedStatistics()) _buildAdvancedAnalytics(),

              if (_permissionService.canViewAdvancedStatistics()) const SizedBox(height: 24),
              if (_permissionService.canViewAdvancedStatistics()) _buildSectionDivider(),
              if (_permissionService.canViewAdvancedStatistics()) const SizedBox(height: 24),

              // القسم الثاني: تحليلات الأداء والاتجاهات
              _buildAnalyticsHeader('تحليلات الأداء والاتجاهات', Icons.trending_up, AppColors.info),
              const SizedBox(height: 16),
              if (_permissionService.canViewAdvancedStatistics()) _buildTrendAnalysis(),

              if (_permissionService.canViewAdvancedStatistics()) const SizedBox(height: 24),
              if (_permissionService.canViewAdvancedStatistics()) _buildSectionDivider(),
              if (_permissionService.canViewAdvancedStatistics()) const SizedBox(height: 24),

              // القسم الثالث: تحليلات أنواع المهام
              _buildAnalyticsHeader('تحليلات أنواع المهام', Icons.category, AppColors.primary),
              const SizedBox(height: 16),
              _buildTaskTypesAnalytics(),

              const SizedBox(height: 24),
              _buildSectionDivider(),
              const SizedBox(height: 24),

              // القسم الرابع: التحليلات التنبؤية والإنذار المبكر
              _buildAnalyticsHeader('التحليلات التنبؤية والإنذار المبكر', Icons.warning, AppColors.warning),
              const SizedBox(height: 16),
              _buildPredictiveAnalytics(),

              const SizedBox(height: 24),
            ],
          ),
        ),
      );
    });
  }





  /// بناء شبكة الإحصائيات المضغوطة الجديدة
  /// تصميم محسن بدون مساحات فارغة مع تباين أفضل
  Widget _buildCompactStatisticsGrid() {
    // فحص الصلاحيات للبيانات الحساسة
    if (!_permissionService.canViewStatistics()) {
      return const SizedBox.shrink();
    }

    final taskStats = _controller.taskStatistics;
    final userStats = _controller.userStatistics;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات العامة',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        // الصف الأول - إحصائيات المهام الأساسية
        Row(
          children: [
            Expanded(child: _buildCompactStatCard(
              title: 'إجمالي المهام',
              value: taskStats.totalTasks.toString(),
              icon: Icons.assignment_outlined,
              color: AppColors.primary,
            )),
            const SizedBox(width: 8),
            Expanded(child: _buildCompactStatCard(
              title: 'مكتملة',
              value: taskStats.completedTasks.toString(),
              icon: Icons.check_circle,
              color: AppColors.success,
              percentage: taskStats.totalTasks > 0
                ? ((taskStats.completedTasks / taskStats.totalTasks) * 100).round()
                : 0,
            )),
            const SizedBox(width: 8),
            Expanded(child: _buildCompactStatCard(
              title: 'قيد التنفيذ',
              value: taskStats.inProgressTasks.toString(),
              icon: Icons.hourglass_empty,
              color: AppColors.warning,
            )),
            const SizedBox(width: 8),
            Expanded(child: _buildCompactStatCard(
              title: 'متأخرة',
              value: taskStats.overdueTasks.toString(),
              icon: Icons.warning_amber,
              color: AppColors.error,
            )),
          ],
        ),
        const SizedBox(height: 8),
        // الصف الثاني - حالات المهام الإضافية
        Row(
          children: [
            Expanded(child: _buildCompactStatCard(
              title: 'في انتظار معلومات',
              value: _getWaitingForInfoCount(taskStats).toString(),
              icon: Icons.info_outline,
              color: AppColors.statusWaitingForInfo,
            )),
            const SizedBox(width: 8),
            Expanded(child: _buildCompactStatCard(
              title: 'ملغاة',
              value: _getCancelledCount(taskStats).toString(),
              icon: Icons.cancel_outlined,
              color: AppColors.statusCancelled,
            )),
            const SizedBox(width: 8),
            Expanded(child: _buildCompactStatCard(
              title: 'قيد الانتظار',
              value: taskStats.pendingTasks.toString(),
              icon: Icons.pending,
              color: AppColors.statusPending,
            )),
            const SizedBox(width: 8),
            Expanded(child: _buildCompactStatCard(
              title: 'جديدة',
              value: _getNewTasksCount(taskStats).toString(),
              icon: Icons.fiber_new,
              color: AppColors.statusNews,
            )),
          ],
        ),
        const SizedBox(height: 8),
        // الصف الثالث - إحصائيات المستخدمين (يظهر فقط مع الصلاحية المناسبة)
        if (_permissionService.canViewAdvancedStatistics())
          Row(
            children: [
              Expanded(child: _buildCompactStatCard(
                title: 'إجمالي المستخدمين',
                value: userStats.totalUsers.toString(),
                icon: Icons.people,
                color: AppColors.info,
              )),
              const SizedBox(width: 8),
              Expanded(child: _buildCompactStatCard(
                title: 'المستخدمين النشطين',
                value: userStats.activeUsers.toString(),
                icon: Icons.person,
                color: AppColors.success,
                percentage: userStats.totalUsers > 0
                  ? ((userStats.activeUsers / userStats.totalUsers) * 100).round()
                  : 0,
              )),
              const SizedBox(width: 8),
              Expanded(child: _buildCompactStatCard(
                title: 'غير نشطين',
                value: (userStats.totalUsers - userStats.activeUsers).toString(),
                icon: Icons.person_off,
                color: AppColors.textSecondary,
              )),
              const SizedBox(width: 8),
              // مساحة فارغة للتوازن
              Expanded(child: Container()),
            ],
          ),
      ],
    );
  }

  /// الحصول على عدد المهام في انتظار المعلومات
  int _getWaitingForInfoCount(TaskStatistics taskStats) {
    return taskStats.tasksByStatus['waiting_for_info'] ?? 0;
  }

  /// الحصول على عدد المهام الملغاة
  int _getCancelledCount(TaskStatistics taskStats) {
    return taskStats.tasksByStatus['cancelled'] ?? 0;
  }

  /// الحصول على عدد المهام الجديدة
  int _getNewTasksCount(TaskStatistics taskStats) {
    return taskStats.tasksByStatus['news'] ?? taskStats.tasksByStatus['new'] ?? 0;
  }

  /// بطاقة إحصائية مضغوطة جديدة
  Widget _buildCompactStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    int? percentage,
  }) {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // الأيقونة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          // المحتوى
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  value,
                  style: AppStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: AppStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                          fontSize: 11,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (percentage != null) ...[
                      const SizedBox(width: 4),
                      Text(
                        '$percentage%',
                        style: AppStyles.bodySmall.copyWith(
                          color: color,
                          fontWeight: FontWeight.w600,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// دالة موحدة لبناء البطاقات
  Widget _buildUnifiedCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    Widget? customContent,
    double? iconSize,
    EdgeInsets? padding,
    double? borderRadius,
    VoidCallback? onTap,
    bool showProgressBar = false,
    double? progressValue,
  }) {
    final cardPadding = padding ?? const EdgeInsets.all(6);
    final cardBorderRadius = borderRadius ?? 8.0;
    final cardIconSize = iconSize ?? 18.0;

    Widget cardContent = Container(
      padding: cardPadding,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(cardBorderRadius),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.15),
            color.withValues(alpha: 0.08),
          ],
        ),
      ),
      child: customContent ?? Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: cardIconSize,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: AppStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: AppStyles.bodySmall.copyWith(
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 1),
            Text(
              subtitle,
              style: AppStyles.labelSmall.copyWith(
                fontSize: 8,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          if (showProgressBar && progressValue != null) ...[
            const SizedBox(height: 3),
            LinearProgressIndicator(
              value: progressValue / 100,
              backgroundColor: AppColors.border,
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 4,
            ),
          ],
        ],
      ),
    );

    Widget card = Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(cardBorderRadius)),
      child: cardContent,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(cardBorderRadius),
        child: card,
      );
    }

    return card;
  }





  /// الحصول على لون الأداء حسب النسبة
  Color _getPerformanceColor(double percentage) {
    if (percentage >= 80) return AppColors.success;
    if (percentage >= 60) return AppColors.warning;
    if (percentage >= 40) return AppColors.accent;
    return AppColors.error;
  }

  /// بناء صف الإجراءات السريعة
  Widget _buildQuickActionsRow() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            if (_permissionService.canCreateTask())
              Expanded(
                child: _buildUnifiedCard(
                  title: 'إنشاء مهمة',
                  value: '',
                  icon: Icons.add_task,
                  color: AppColors.primary,
                  onTap: () => Get.toNamed('/task/create'),
                  customContent: _buildActionContent('إنشاء مهمة', Icons.add_task, AppColors.primary),
                ),
              ),
            if (_permissionService.canCreateTask() && _permissionService.canAccessReports())
              const SizedBox(width: 8),
            if (_permissionService.canAccessReports())
              Expanded(
                child: _buildUnifiedCard(
                  title: 'عرض التقارير',
                  value: '',
                  icon: Icons.analytics,
                  color: AppColors.info,
                  onTap: () => Get.toNamed('/reports'),
                  customContent: _buildActionContent('عرض التقارير', Icons.analytics, AppColors.info),
                ),
              ),
            if ((_permissionService.canCreateTask() || _permissionService.canAccessReports()) &&
                _permissionService.canAccessChat())
              const SizedBox(width: 8),
            if (_permissionService.canAccessChat())
              Expanded(
                child: _buildUnifiedCard(
                  title: 'المحادثات',
                  value: '',
                  icon: Icons.chat,
                  color: AppColors.accent,
                  onTap: () => Get.toNamed('/chat/unified-list'),
                  customContent: _buildActionContent('المحادثات', Icons.chat, AppColors.accent),
                ),
              ),
            if (_permissionService.canAccessChat() && _permissionService.canAccessSearch())
              const SizedBox(width: 8),
            if (_permissionService.canAccessSearch())
              Expanded(
                child: _buildUnifiedCard(
                  title: 'البحث',
                  value: '',
                  icon: Icons.search,
                  color: AppColors.success,
                  onTap: () => Get.toNamed('/search'),
                  customContent: _buildActionContent('البحث', Icons.search, AppColors.success),
                ),
              ),
          ],
        ),
      ],
    );
  }

  /// بناء محتوى الإجراء
  Widget _buildActionContent(String title, IconData icon, Color color) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: AppStyles.bodySmall.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 10,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// بناء محتوى الإحصائيات المتقدمة
  Widget _buildInsightContent(String title, Widget content, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: AppStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        content,
      ],
    );
  }



  /// بناء مؤشرات صحة النظام
  /// مؤشرات الصحة محسوبة من البيانات الحقيقية باستخدام خوارزميات تقييم الأداء
  Widget _buildSystemHealthIndicators() {
    // فحص الصلاحيات للوصول لإعدادات النظام
    if (!_permissionService.canViewAdvancedStatistics()) {
      return const SizedBox.shrink();
    }

    final taskStats = _controller.taskStatistics;
    final userStats = _controller.userStatistics;

    // حساب مؤشرات الصحة
    final systemHealth = _calculateSystemHealth(taskStats, userStats);
    final taskHealth = _calculateTaskHealth(taskStats);
    final userHealth = _calculateUserHealth(userStats);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'صحة النظام',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildHealthIndicator(
                  'الصحة العامة',
                  systemHealth,
                  Icons.health_and_safety,
                ),
                const SizedBox(height: 12),
                _buildHealthIndicator(
                  'صحة المهام',
                  taskHealth,
                  Icons.task_alt,
                ),
                const SizedBox(height: 12),
                _buildHealthIndicator(
                  'نشاط المستخدمين',
                  userHealth,
                  Icons.people,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء مؤشر صحة
  Widget _buildHealthIndicator(String title, double health, IconData icon) {
    final color = _getHealthColor(health);
    final status = _getHealthStatus(health);

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: AppStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    status,
                    style: AppStyles.bodySmall.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: health / 100,
                backgroundColor: AppColors.border,
                valueColor: AlwaysStoppedAnimation<Color>(color),
                minHeight: 6,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// حساب الصحة العامة للنظام
  double _calculateSystemHealth(TaskStatistics taskStats, UserStatistics userStats) {
    final taskHealth = _calculateTaskHealth(taskStats);
    final userHealth = _calculateUserHealth(userStats);
    return (taskHealth + userHealth) / 2;
  }

  /// حساب صحة المهام
  double _calculateTaskHealth(TaskStatistics taskStats) {
    if (taskStats.totalTasks == 0) return 100.0;

    final completionRate = (taskStats.completedTasks / taskStats.totalTasks) * 100;
    final overdueRate = (taskStats.overdueTasks / taskStats.totalTasks) * 100;

    // صحة المهام = معدل الإنجاز - معدل التأخير
    return (completionRate - (overdueRate * 0.5)).clamp(0.0, 100.0);
  }

  /// حساب صحة المستخدمين
  double _calculateUserHealth(UserStatistics userStats) {
    if (userStats.totalUsers == 0) return 100.0;
    return (userStats.activeUsers / userStats.totalUsers) * 100;
  }

  /// الحصول على لون الصحة
  Color _getHealthColor(double health) {
    if (health >= 80) return AppColors.success;
    if (health >= 60) return AppColors.warning;
    if (health >= 40) return AppColors.accent;
    return AppColors.error;
  }

  /// الحصول على حالة الصحة
  String _getHealthStatus(double health) {
    if (health >= 80) return 'ممتاز';
    if (health >= 60) return 'جيد';
    if (health >= 40) return 'متوسط';
    return 'يحتاج تحسين';
  }

  /// بناء الإحصائيات المتقدمة
  /// جميع الحسابات مبنية على البيانات الحقيقية من قاعدة البيانات
  Widget _buildAdvancedInsights() {
    // فحص الصلاحيات للإحصائيات المتقدمة
    if (!_permissionService.canViewAdvancedStatistics()) {
      return const SizedBox.shrink();
    }

    final taskStats = _controller.taskStatistics;
    final userStats = _controller.userStatistics;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات متقدمة',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // بطاقة توزيع المهام
            Expanded(
              child: _buildUnifiedCard(
                title: 'توزيع المهام',
                value: '',
                icon: Icons.pie_chart,
                color: AppColors.primary,
                customContent: _buildInsightContent('توزيع المهام', _buildTaskDistributionContent(taskStats), Icons.pie_chart, AppColors.primary),
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(width: 12),
            // بطاقة كفاءة الفريق
            Expanded(
              child: _buildUnifiedCard(
                title: 'كفاءة الفريق',
                value: '',
                icon: Icons.groups,
                color: AppColors.success,
                customContent: _buildInsightContent('كفاءة الفريق', _buildTeamEfficiencyContent(taskStats, userStats), Icons.groups, AppColors.success),
                padding: const EdgeInsets.all(16),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // بطاقة معدل الإنتاجية
            Expanded(
              child: _buildUnifiedCard(
                title: 'معدل الإنتاجية',
                value: '',
                icon: Icons.trending_up,
                color: AppColors.info,
                customContent: _buildInsightContent('معدل الإنتاجية', _buildProductivityContent(taskStats, userStats), Icons.trending_up, AppColors.info),
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(width: 12),
            // بطاقة نقاط التحسين
            Expanded(
              child: _buildUnifiedCard(
                title: 'نقاط التحسين',
                value: '',
                icon: Icons.lightbulb,
                color: AppColors.warning,
                customContent: _buildInsightContent('نقاط التحسين', _buildImprovementContent(taskStats), Icons.lightbulb, AppColors.warning),
                padding: const EdgeInsets.all(16),
              ),
            ),
          ],
        ),
      ],
    );
  }



  /// محتوى توزيع المهام
  Widget _buildTaskDistributionContent(TaskStatistics taskStats) {
    if (taskStats.totalTasks == 0) {
      return Text(
        'لا توجد مهام',
        style: AppStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      );
    }

    final completedPercentage = (taskStats.completedTasks / taskStats.totalTasks * 100);
    final inProgressPercentage = (taskStats.inProgressTasks / taskStats.totalTasks * 100);
    final pendingPercentage = (taskStats.pendingTasks / taskStats.totalTasks * 100);
    final waitingForInfoPercentage = (_getWaitingForInfoCount(taskStats) / taskStats.totalTasks * 100);
    final cancelledPercentage = (_getCancelledCount(taskStats) / taskStats.totalTasks * 100);
    final newTasksPercentage = (_getNewTasksCount(taskStats) / taskStats.totalTasks * 100);

    return Column(
      children: [
        _buildDistributionItem('مكتملة', completedPercentage, AppColors.success),
        const SizedBox(height: 6),
        _buildDistributionItem('قيد التنفيذ', inProgressPercentage, AppColors.warning),
        const SizedBox(height: 6),
        _buildDistributionItem('في انتظار معلومات', waitingForInfoPercentage, AppColors.statusWaitingForInfo),
        const SizedBox(height: 6),
        _buildDistributionItem('قيد الانتظار', pendingPercentage, AppColors.statusPending),
        const SizedBox(height: 6),
        _buildDistributionItem('ملغاة', cancelledPercentage, AppColors.statusCancelled),
        const SizedBox(height: 6),
        _buildDistributionItem('جديدة', newTasksPercentage, AppColors.statusNews),
      ],
    );
  }

  /// عنصر توزيع
  Widget _buildDistributionItem(String label, double percentage, Color color) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: AppStyles.bodySmall,
          ),
        ),
        Expanded(
          flex: 3,
          child: LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: AppColors.border,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 4,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${percentage.toStringAsFixed(1)}%',
          style: AppStyles.bodySmall.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// محتوى كفاءة الفريق
  Widget _buildTeamEfficiencyContent(TaskStatistics taskStats, UserStatistics userStats) {
    if (userStats.totalUsers == 0) {
      return Text(
        'لا يوجد مستخدمون',
        style: AppStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      );
    }

    final tasksPerUser = taskStats.totalTasks / userStats.totalUsers;
    final activeUserRatio = userStats.activeUsers / userStats.totalUsers;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'متوسط المهام لكل مستخدم',
          style: AppStyles.bodySmall,
        ),
        Text(
          tasksPerUser.toStringAsFixed(1),
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.success,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'نسبة المستخدمين النشطين',
          style: AppStyles.bodySmall,
        ),
        Text(
          '${(activeUserRatio * 100).toStringAsFixed(1)}%',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.success,
          ),
        ),
      ],
    );
  }

  /// محتوى الإنتاجية
  Widget _buildProductivityContent(TaskStatistics taskStats, UserStatistics userStats) {
    if (taskStats.totalTasks == 0 || userStats.activeUsers == 0) {
      return Text(
        'غير متاح',
        style: AppStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      );
    }

    final completionRate = taskStats.completedTasks / taskStats.totalTasks;
    final productivityScore = (completionRate * userStats.activeUsers / userStats.totalUsers) * 100;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نقاط الإنتاجية',
          style: AppStyles.bodySmall,
        ),
        Text(
          productivityScore.toStringAsFixed(1),
          style: AppStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.info,
          ),
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: (productivityScore / 100).clamp(0.0, 1.0),
          backgroundColor: AppColors.border,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.info),
          minHeight: 4,
        ),
      ],
    );
  }

  /// محتوى نقاط التحسين
  Widget _buildImprovementContent(TaskStatistics taskStats) {
    final improvements = <String>[];

    if (taskStats.totalTasks > 0) {
      final overdueRate = taskStats.overdueTasks / taskStats.totalTasks;
      if (overdueRate > 0.2) {
        improvements.add('تقليل المهام المتأخرة');
      }

      final completionRate = taskStats.completedTasks / taskStats.totalTasks;
      if (completionRate < 0.7) {
        improvements.add('تحسين معدل الإنجاز');
      }

      final pendingRate = taskStats.pendingTasks / taskStats.totalTasks;
      if (pendingRate > 0.3) {
        improvements.add('تسريع بدء المهام');
      }
    }

    if (improvements.isEmpty) {
      improvements.add('الأداء ممتاز');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: improvements.take(3).map((improvement) => Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Row(
          children: [
            Icon(
              Icons.arrow_forward_ios,
              size: 12,
              color: AppColors.warning,
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                improvement,
                style: AppStyles.bodySmall,
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }









  /// بناء قسم النشاط الأخير والسجلات
  /// يعرض بيانات حقيقية من APIs النظام
  Widget _buildRecentActivitySection() {
    // فحص الصلاحيات لعرض النشاط
    if (!_permissionService.canViewStatistics()) {
      return const SizedBox.shrink();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النشاط الأخير',
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // بطاقة آخر الأنشطة
            Expanded(
              flex: 2,
              child: _buildActivityCard(),
            ),
            const SizedBox(width: 12),
            // بطاقة المهام الحديثة والتذكيرات
            Expanded(
              child: _buildTasksAndRemindersCard(),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة الأنشطة الأخيرة
  Widget _buildActivityCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary.withValues(alpha: 0.05),
              AppColors.primary.withValues(alpha: 0.02),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'آخر الأنشطة',
                  style: AppStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Get.toNamed('/activity-logs'),
                  child: Text(
                    'عرض الكل',
                    style: AppStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // تحميل البيانات حسب الصلاحيات فقط
            if (_permissionService.canViewAdvancedStatistics())
              FutureBuilder<List<ActivityLog>>(
                future: ActivityLogsApiService().getRecentActivityLogs(limit: 5),
                builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                if (snapshot.hasError) {
                  return _buildEmptyActivityState('خطأ في تحميل الأنشطة');
                }

                final activities = snapshot.data ?? [];
                if (activities.isEmpty) {
                  return _buildEmptyActivityState('لا توجد أنشطة حديثة');
                }

                return Column(
                  children: activities.map((activity) =>
                    _buildRecentActivityItem(activity)
                  ).toList(),
                );
              },
            )
            else
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Text(
                    'ليس لديك صلاحية لعرض سجلات النشاط',
                    style: AppStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة المهام والتذكيرات
  Widget _buildTasksAndRemindersCard() {
    return Column(
      children: [
        // بطاقة المهام الحديثة
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.success.withValues(alpha: 0.1),
                  AppColors.success.withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.fiber_new,
                      color: AppColors.success,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'المهام الحديثة',
                      style: AppStyles.titleSmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),  
                FutureBuilder<List<Task>>(
                  future: _getRecentTasks(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const SizedBox(
                        height: 40,
                        child: Center(child: CircularProgressIndicator()),
                      );
                    }

                    final tasks = snapshot.data ?? [];
                    if (tasks.isEmpty) {
                      return Text(
                        'لا توجد مهام حديثة',
                        style: AppStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      );
                    }

                    return Column(
                      children: [
                        ...tasks.take(2).map((task) =>
                          _buildTaskItem(task)
                        ),
                        if (tasks.length > 2) ...[
                          const SizedBox(height: 4),
                          TextButton(
                            onPressed: () => Get.toNamed('/tasks'),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              minimumSize: Size.zero,
                            ),
                            child: Text(
                              'عرض ${tasks.length - 2} مهمة أخرى',
                              style: AppStyles.bodySmall.copyWith(
                                color: AppColors.success,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        // بطاقة التذكيرات
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.warning.withValues(alpha: 0.1),
                  AppColors.warning.withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      color: AppColors.warning,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تذكيرات المواعيد',
                      style: AppStyles.titleSmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.warning,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                FutureBuilder<List<Task>>(
                  future: _getTasksDueSoon(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const SizedBox(
                        height: 40,
                        child: Center(child: CircularProgressIndicator()),
                      );
                    }

                    final dueTasks = snapshot.data ?? [];
                    if (dueTasks.isEmpty) {
                      return Text(
                        'لا توجد مواعيد قريبة',
                        style: AppStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      );
                    }

                    return Column(
                      children: [
                        ...dueTasks.take(2).map((task) =>
                          _buildReminderItem(task)
                        ),
                        if (dueTasks.length > 2) ...[
                          const SizedBox(height: 4),
                          TextButton(
                            onPressed: () => Get.toNamed('/tasks', arguments: {'filter': 'due_soon'}),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              minimumSize: Size.zero,
                            ),
                            child: Text(
                              'عرض ${dueTasks.length - 2} تذكير آخر',
                              style: AppStyles.bodySmall.copyWith(
                                color: AppColors.warning,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء عنصر نشاط حديث
  Widget _buildRecentActivityItem(ActivityLog activity) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: _getActivityColor(activity.action).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              _getActivityIcon(activity.action),
              color: _getActivityColor(activity.action),
              size: 14,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getActivityTitle(activity),
                  style: AppStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  _formatActivityTime(activity.timestamp),
                  style: AppStyles.labelSmall.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر مهمة
  Widget _buildTaskItem(Task task) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => Get.toNamed('/task/details', arguments: {'taskId': task.id}),
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: _getTaskStatusColor(task.status).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // رقم المهمة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '#${task.id}',
                    style: AppStyles.labelSmall.copyWith(
                      color: AppColors.primary,
                      fontSize: 9,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                // حالة المهمة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getTaskStatusColor(task.status).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getTaskStatusText(task.status),
                    style: AppStyles.labelSmall.copyWith(
                      color: _getTaskStatusColor(task.status),
                      fontSize: 9,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
                // أيقونة المهمة
                Icon(
                  _getTaskStatusIcon(task.status),
                  color: _getTaskStatusColor(task.status),
                  size: 12,
                ),
              ],
            ),
            const SizedBox(height: 4),
            // عنوان المهمة
            Text(
              task.title,
              style: AppStyles.bodySmall.copyWith(
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        ),
      ),
    );
  }

  /// بناء عنصر تذكير
  Widget _buildReminderItem(Task task) {
    final urgencyLevel = _getTaskUrgencyLevel(task.dueDate);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => Get.toNamed('/task/details', arguments: {'taskId': task.id}),
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: urgencyLevel['color'].withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: urgencyLevel['color'].withValues(alpha: 0.3),
              width: 1,
            ),
          ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // رقم المهمة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(3),
                  ),
                  child: Text(
                    '#${task.id}',
                    style: AppStyles.labelSmall.copyWith(
                      color: AppColors.primary,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                // مستوى الإلحاح
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: urgencyLevel['color'].withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(3),
                  ),
                  child: Text(
                    urgencyLevel['label'],
                    style: AppStyles.labelSmall.copyWith(
                      color: urgencyLevel['color'],
                      fontSize: 8,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
                Icon(
                  urgencyLevel['icon'],
                  color: urgencyLevel['color'],
                  size: 12,
                ),
              ],
            ),
            const SizedBox(height: 4),
            // عنوان المهمة
            Text(
              task.title,
              style: AppStyles.bodySmall.copyWith(
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            if (task.dueDate != null) ...[
              const SizedBox(height: 2),
              Text(
                _formatDueDate(task.dueDate!),
                style: AppStyles.labelSmall.copyWith(
                  color: urgencyLevel['color'],
                  fontSize: 9,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
        ),
      ),
    );
  }

  /// بناء حالة فارغة للأنشطة
  Widget _buildEmptyActivityState(String message) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 40,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون النشاط
  Color _getActivityColor(String action) {
    switch (action.toLowerCase()) {
      case 'created':
        return AppColors.success;
      case 'updated':
        return AppColors.info;
      case 'completed':
        return AppColors.primary;
      case 'deleted':
        return AppColors.error;
      case 'assigned':
        return AppColors.accent;
      default:
        return AppColors.textSecondary;
    }
  }

  /// الحصول على أيقونة النشاط
  IconData _getActivityIcon(String action) {
    switch (action.toLowerCase()) {
      case 'created':
        return Icons.add_circle_outline;
      case 'updated':
        return Icons.edit_outlined;
      case 'completed':
        return Icons.check_circle_outline;
      case 'deleted':
        return Icons.delete_outline;
      case 'assigned':
        return Icons.person_add_outlined;
      default:
        return Icons.info_outline;
    }
  }

  /// الحصول على عنوان النشاط
  String _getActivityTitle(ActivityLog activity) {
    if (activity.changeDescription?.isNotEmpty == true) {
      return activity.changeDescription!;
    }
    return activity.details ?? 'نشاط ${activity.action}';
  }

  /// تنسيق وقت النشاط
  String _formatActivityTime(int timestamp) {
    final now = DateTime.now();
    final activityTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final difference = now.difference(activityTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  /// تنسيق الموعد النهائي
  String _formatDueDate(int dueDate) {
    final now = DateTime.now();
    final due = DateTime.fromMillisecondsSinceEpoch(dueDate * 1000);
    final difference = due.difference(now);

    if (difference.inHours < 24) {
      return 'خلال ${difference.inHours} ساعة';
    } else {
      return 'خلال ${difference.inDays} يوم';
    }
  }

  /// الحصول على المهام الحديثة (آخر 48 ساعة)
  Future<List<Task>> _getRecentTasks() async {
    try {
      // استخدام المهام المفلترة من TaskController بدلاً من API مباشرة
      final taskController = Get.find<TaskController>();
      final allTasks = taskController.allTasks; // المهام المفلترة حسب الصلاحيات

      // طباعة تشخيصية للمقارنة
      debugPrint('🔍 [COMPREHENSIVE_DASHBOARD] _getRecentTasks - المهام المتاحة: ${allTasks.length}');

      final now = DateTime.now();
      final fortyEightHoursAgo = now.subtract(const Duration(hours: 48));
      final fortyEightHoursAgoTimestamp = fortyEightHoursAgo.millisecondsSinceEpoch ~/ 1000;

      // فلترة المهام المنشأة في آخر 48 ساعة واستبعاد المكتملة والملغاة
      final recentTasks = allTasks.where((task) {
        final isRecent = task.createdAt > fortyEightHoursAgoTimestamp;
        final isNotCompleted = task.status != 'completed' && task.status != 'cancelled';
        return isRecent && isNotCompleted;
      }).toList();

      // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
      recentTasks.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // عرض أول 2 مهمة في البطاقة، مع ضمان وجود 5 مهام كحد أدنى للعرض الكامل
      return recentTasks.take(2).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام الحديثة: $e');
      return [];
    }
  }

  /// الحصول على المهام المستحقة اليوم
  Future<int> _getTasksDueToday() async {
    try {
      // استخدام المهام المفلترة من TaskController بدلاً من API مباشرة
      final taskController = Get.find<TaskController>();
      final allTasks = taskController.allTasks; // المهام المفلترة حسب الصلاحيات
      final now = DateTime.now();
      final todayStart = DateTime(now.year, now.month, now.day);
      final todayEnd = todayStart.add(const Duration(days: 1));
      final todayStartTimestamp = todayStart.millisecondsSinceEpoch ~/ 1000;
      final todayEndTimestamp = todayEnd.millisecondsSinceEpoch ~/ 1000;

      return allTasks.where((task) {
        final hasDueDate = task.dueDate != null;
        final isDueToday = hasDueDate &&
                          task.dueDate! >= todayStartTimestamp &&
                          task.dueDate! < todayEndTimestamp;
        final isNotCompleted = task.status != 'completed' && task.status != 'cancelled';
        return isDueToday && isNotCompleted;
      }).length;
    } catch (e) {
      debugPrint('خطأ في حساب المهام المستحقة اليوم: $e');
      return 0;
    }
  }

  /// الحصول على المهام المستحقة غداً
  Future<int> _getTasksDueTomorrow() async {
    try {
      // استخدام المهام المفلترة من TaskController بدلاً من API مباشرة
      final taskController = Get.find<TaskController>();
      final allTasks = taskController.allTasks; // المهام المفلترة حسب الصلاحيات
      final now = DateTime.now();
      final tomorrowStart = DateTime(now.year, now.month, now.day).add(const Duration(days: 1));
      final tomorrowEnd = tomorrowStart.add(const Duration(days: 1));
      final tomorrowStartTimestamp = tomorrowStart.millisecondsSinceEpoch ~/ 1000;
      final tomorrowEndTimestamp = tomorrowEnd.millisecondsSinceEpoch ~/ 1000;

      return allTasks.where((task) {
        final hasDueDate = task.dueDate != null;
        final isDueTomorrow = hasDueDate &&
                             task.dueDate! >= tomorrowStartTimestamp &&
                             task.dueDate! < tomorrowEndTimestamp;
        final isNotCompleted = task.status != 'completed' && task.status != 'cancelled';
        return isDueTomorrow && isNotCompleted;
      }).length;
    } catch (e) {
      debugPrint('خطأ في حساب المهام المستحقة غداً: $e');
      return 0;
    }
  }

  /// الحصول على المهام المستحقة بعد يومين
  Future<int> _getTasksDueAfterTomorrow() async {
    try {
      // استخدام المهام المفلترة من TaskController بدلاً من API مباشرة
      final taskController = Get.find<TaskController>();
      final allTasks = taskController.allTasks; // المهام المفلترة حسب الصلاحيات
      final now = DateTime.now();
      final afterTomorrowStart = DateTime(now.year, now.month, now.day).add(const Duration(days: 2));
      final afterTomorrowEnd = afterTomorrowStart.add(const Duration(days: 1));
      final afterTomorrowStartTimestamp = afterTomorrowStart.millisecondsSinceEpoch ~/ 1000;
      final afterTomorrowEndTimestamp = afterTomorrowEnd.millisecondsSinceEpoch ~/ 1000;

      return allTasks.where((task) {
        final hasDueDate = task.dueDate != null;
        final isDueAfterTomorrow = hasDueDate &&
                                  task.dueDate! >= afterTomorrowStartTimestamp &&
                                  task.dueDate! < afterTomorrowEndTimestamp;
        final isNotCompleted = task.status != 'completed' && task.status != 'cancelled';
        return isDueAfterTomorrow && isNotCompleted;
      }).length;
    } catch (e) {
      debugPrint('خطأ في حساب المهام المستحقة بعد يومين: $e');
      return 0;
    }
  }

  /// الحصول على المهام المستحقة قريباً
  Future<List<Task>> _getTasksDueSoon() async {
    try {
      // استخدام المهام المفلترة من TaskController بدلاً من API مباشرة
      final taskController = Get.find<TaskController>();
      final allTasks = taskController.allTasks; // المهام المفلترة حسب الصلاحيات

      final now = DateTime.now();
      final nextWeek = now.add(const Duration(days: 7));
      final nowTimestamp = now.millisecondsSinceEpoch ~/ 1000;
      final nextWeekTimestamp = nextWeek.millisecondsSinceEpoch ~/ 1000;

      // فلترة المهام المستحقة في الأسبوع القادم
      final tasksDueSoon = allTasks.where((task) {
        if (task.dueDate == null) return false;
        return task.dueDate! >= nowTimestamp && task.dueDate! <= nextWeekTimestamp;
      }).toList();

      // ترتيب حسب تاريخ الاستحقاق
      tasksDueSoon.sort((a, b) => a.dueDate!.compareTo(b.dueDate!));

      return tasksDueSoon;
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام المستحقة قريباً: $e');
      return [];
    }
  }

  /// الحصول على لون حالة المهمة
  Color _getTaskStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'مكتملة':
      case 'completed':
        return AppColors.success;
      case 'قيد التنفيذ':
      case 'in_progress':
        return AppColors.info;
      case 'قيد الانتظار':
      case 'pending':
        return AppColors.warning;
      case 'ملغية':
      case 'cancelled':
        return AppColors.error;
      case 'waiting':
        return AppColors.accent;
      default:
        return AppColors.textSecondary;
    }
  }

  /// الحصول على نص حالة المهمة
  String _getTaskStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'مكتملة';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'pending':
        return 'قيد الانتظار';
      case 'cancelled':
        return 'ملغية';
      case 'waiting':
        return 'قيد الانتظار';
      default:
        return status;
    }
  }

  /// الحصول على أيقونة حالة المهمة
  IconData _getTaskStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'مكتملة':
      case 'completed':
        return Icons.check_circle;
      case 'قيد التنفيذ':
      case 'in_progress':
        return Icons.play_circle;
      case 'قيد الانتظار':
      case 'pending':
        return Icons.pause_circle;
      case ' ملغاه':
      case 'cancelled':
        return Icons.cancel;
      case 'waiting':
        return Icons.schedule;
      default:
        return Icons.task_alt;
    }
  }

  /// الحصول على مستوى إلحاح المهمة بناءً على الموعد النهائي
  Map<String, dynamic> _getTaskUrgencyLevel(int? dueDate) {
    if (dueDate == null) {
      return {
        'label': 'بدون موعد',
        'color': AppColors.textSecondary,
        'icon': Icons.schedule_outlined,
      };
    }

    final now = DateTime.now();
    final due = DateTime.fromMillisecondsSinceEpoch(dueDate * 1000);
    final difference = due.difference(now);

    if (difference.isNegative) {
      return {
        'label': 'متأخرة',
        'color': AppColors.error,
        'icon': Icons.warning,
      };
    } else if (difference.inHours <= 6) {
      return {
        'label': 'عاجل جداً',
        'color': AppColors.error,
        'icon': Icons.priority_high,
      };
    } else if (difference.inHours <= 24) {
      return {
        'label': 'عاجل',
        'color': AppColors.warning,
        'icon': Icons.alarm,
      };
    } else if (difference.inDays <= 2) {
      return {
        'label': 'قريب',
        'color': AppColors.accent,
        'icon': Icons.schedule,
      };
    } else {
      return {
        'label': 'عادي',
        'color': AppColors.info,
        'icon': Icons.schedule_outlined,
      };
    }
  }

  /// بناء شبكة المخططات باستخدام مدير التخطيط المتقدم
  Widget _buildChartsGrid() {
    if (_controller.chartCards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مخططات للعرض',
              style: AppStyles.headlineSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تأكد من وجود بيانات في النظام',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _controller.refreshDashboard(),
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث البيانات'),
            ),
          ],
        ),
      );
    }

    // استخدام مدير التخطيط المتقدم مع Obx للتفاعل مع التحديثات
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.8, // تحديد ارتفاع مناسب
      child: Obx(() => DashboardLayoutManager(
        key: ValueKey('dashboard_layout_${_controller.chartCards.length}_${_controller.chartCards.map((c) => '${c.id}_${c.lastUpdated.millisecondsSinceEpoch}').join('_')}'),
        cards: _controller.chartCards,
        onCardsReordered: (reorderedCards) {
          _controller.reorderChartCards(reorderedCards);
        },
        onCardUpdated: (updatedCard) {
          _controller.updateCard(updatedCard);
        },
        onRefresh: () {
          _controller.refreshDashboard();
        },
        onRefreshSingle: (cardId) {
          _controller.refreshSingleCard(cardId);
        },
      )),
    );
  }

  /// بناء التحليلات المتقدمة - النظرة العامة
  Widget _buildAdvancedAnalytics() {
    final taskStats = _controller.taskStatistics;
    final userStats = _controller.userStatistics;
    final waitingForInfoCount = _getWaitingForInfoCount(taskStats);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // الصف الأول - مؤشرات الحالة الحرجة
        Row(
          children: [
            Expanded(
              child: _buildCriticalStatusCard(
                'في انتظار معلومات',
                waitingForInfoCount,
                Icons.info_outline,
                AppColors.statusWaitingForInfo,
                waitingForInfoCount > 0
                  ? 'تحتاج هذه المهام إلى معلومات إضافية للمتابعة'
                  : 'جميع المهام لديها المعلومات المطلوبة',
                waitingForInfoCount > 0 ? 'يُنصح بالمتابعة' : null,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildCriticalStatusCard(
                'المهام الملغاة',
                _getCancelledCount(taskStats),
                Icons.cancel_outlined,
                AppColors.statusCancelled,
                _getCancelledCount(taskStats) > 0
                  ? 'مهام تم إلغاؤها لأسباب مختلفة'
                  : 'لا توجد مهام ملغاة',
                _getCancelledCount(taskStats) > 0 ? 'مراجعة الأسباب' : null,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // الصف الثاني - تحليل شامل للحالات والمستخدمين
        Row(
          children: [
            // بطاقة تحليل حالات المهام
            Expanded(
              flex: 2,
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.analytics_outlined,
                            color: AppColors.primary,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'تحليل حالات المهام',
                            style: AppStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildStatusAnalysisItem('مكتملة', taskStats.completedTasks, AppColors.success),
                      _buildStatusAnalysisItem('قيد التنفيذ', taskStats.inProgressTasks, AppColors.warning),
                      _buildStatusAnalysisItem('في انتظار معلومات', waitingForInfoCount, AppColors.statusWaitingForInfo),
                      _buildStatusAnalysisItem('قيد الانتظار', taskStats.pendingTasks, AppColors.statusPending),
                      _buildStatusAnalysisItem('ملغاة', _getCancelledCount(taskStats), AppColors.statusCancelled),
                      _buildStatusAnalysisItem('جديدة', _getNewTasksCount(taskStats), AppColors.statusNews),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // بطاقة إحصائيات المستخدمين
            Expanded(
              flex: 1,
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.people,
                            color: AppColors.info,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'المستخدمين',
                            style: AppStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.info,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      _buildUserMetric(
                        'إجمالي المستخدمين',
                        userStats.totalUsers,
                        Icons.people,
                        AppColors.info,
                      ),
                      const SizedBox(height: 12),

                      _buildUserMetric(
                        'المستخدمين النشطين',
                        userStats.activeUsers,
                        Icons.person,
                        AppColors.success,
                      ),
                      const SizedBox(height: 12),

                      _buildUserMetric(
                        'غير نشطين',
                        userStats.totalUsers - userStats.activeUsers,
                        Icons.person_off,
                        AppColors.textSecondary,
                      ),

                      const SizedBox(height: 16),

                      // نسبة النشاط
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.trending_up,
                              color: AppColors.success,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'نسبة النشاط: ${userStats.totalUsers > 0 ? ((userStats.activeUsers / userStats.totalUsers) * 100).round() : 0}%',
                              style: AppStyles.bodySmall.copyWith(
                                color: AppColors.success,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة الحالة الحرجة
  Widget _buildCriticalStatusCard(
    String title,
    int count,
    IconData icon,
    Color color,
    String description,
    String? actionText,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: AppStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '$count مهمة',
              style: AppStyles.headingLarge.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            if (actionText != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: color.withValues(alpha: 0.3)),
                ),
                child: Text(
                  actionText,
                  style: AppStyles.bodySmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// مؤشر المستخدمين
  Widget _buildUserMetric(String title, int value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: AppStyles.bodyMedium,
          ),
        ),
        Text(
          '$value',
          style: AppStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// عنصر تحليل الحالة
  Widget _buildStatusAnalysisItem(String status, int count, Color color) {
    final total = _controller.taskStatistics.totalTasks;
    final percentage = total > 0 ? (count / total * 100).round() : 0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              status,
              style: AppStyles.bodySmall,
            ),
          ),
          Text(
            '$count ($percentage%)',
            style: AppStyles.bodySmall.copyWith(
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تحليل الأداء والاتجاهات الزمنية
  Widget _buildTrendAnalysis() {
    final timeAnalytics = _controller.timePerformanceAnalytics;
    final collaborationAnalytics = _controller.collaborationAnalytics;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // القسم الفرعي الأول: الأداء الزمني
        _buildSubSectionHeader(
          'مؤشرات الأداء الزمني',
          Icons.schedule,
          AppColors.info,
          'تحليل أوقات الإنجاز والاتجاهات الزمنية',
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildTimePerformanceCard(timeAnalytics),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildWeeklyTrendsCard(timeAnalytics),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // القسم الفرعي الثاني: تحليلات التعاون
        _buildSubSectionHeader(
          'تحليلات التعاون والمساهمة',
          Icons.group_work,
          AppColors.primary,
          'تحليل مستوى التفاعل والمساهمة في المهام',
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildCollaborationStatsCard(collaborationAnalytics),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActiveContributorsCard(collaborationAnalytics),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء رأس القسم الفرعي
  Widget _buildSubSectionHeader(String title, IconData icon, Color color, String description) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بطاقة مؤشرات الأداء الزمني
  Widget _buildTimePerformanceCard(TimePerformanceAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'مؤشرات الأداء الزمني',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildPerformanceMetric(
              'متوسط وقت الإنجاز',
              '${analytics.averageCompletionTime.toStringAsFixed(1)} يوم',
              Icons.timer,
              AppColors.info,
            ),
            const SizedBox(height: 12),

            _buildPerformanceMetric(
              'الإنجازات الأخيرة (30 يوم)',
              '${analytics.recentCompletions} مهمة',
              Icons.trending_up,
              AppColors.success,
            ),
            const SizedBox(height: 12),

            _buildPerformanceMetric(
              'المهام المتأخرة',
              '${analytics.totalOverdueTasks} مهمة',
              Icons.warning,
              AppColors.error,
            ),

            if (analytics.averageOverdueDays > 0) ...[
              const SizedBox(height: 8),
              Text(
                'متوسط التأخير: ${analytics.averageOverdueDays.toStringAsFixed(1)} يوم',
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.error,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// مؤشر أداء فردي
  Widget _buildPerformanceMetric(String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: AppStyles.bodyMedium,
          ),
        ),
        Text(
          value,
          style: AppStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// بطاقة الاتجاهات الأسبوعية
  Widget _buildWeeklyTrendsCard(TimePerformanceAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.show_chart,
                  color: AppColors.accent,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'الاتجاهات الأسبوعية',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.accent,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (analytics.weeklyCompletions.isNotEmpty) ...[
              ...analytics.weeklyCompletions.map((week) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        week.week,
                        style: AppStyles.bodyMedium,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.accent.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${week.completions}',
                        style: AppStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.accent,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ] else ...[
              Text(
                'لا توجد بيانات كافية للاتجاهات',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بطاقة إحصائيات التعاون
  Widget _buildCollaborationStatsCard(CollaborationAnalytics analytics) {
    final stats = analytics.collaborationStats;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.group_work,
                  color: Colors.purple,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات التعاون',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildPerformanceMetric(
              'إجمالي المساهمين',
              '${stats.totalContributors}',
              Icons.people,
              Colors.purple,
            ),
            const SizedBox(height: 12),

            _buildPerformanceMetric(
              'متوسط المساهمين/مهمة',
              stats.averageContributorsPerTask.toStringAsFixed(1),
              Icons.person_add,
              Colors.blue,
            ),
            const SizedBox(height: 12),

            _buildPerformanceMetric(
              'مهام متعددة المساهمين',
              '${stats.tasksWithMultipleContributors}',
              Icons.groups,
              Colors.green,
            ),
            const SizedBox(height: 12),

            _buildPerformanceMetric(
              'إجمالي التعليقات',
              '${stats.totalComments}',
              Icons.comment,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة المساهمين النشطين
  Widget _buildActiveContributorsCard(CollaborationAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'أكثر المساهمين نشاطاً',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.amber.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (analytics.activeContributors.isNotEmpty) ...[
              ...analytics.activeContributors.take(5).map((contributor) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 12,
                      backgroundColor: Colors.amber.withValues(alpha: 0.2),
                      child: Text(
                        contributor.userName.isNotEmpty
                          ? contributor.userName[0].toUpperCase()
                          : '؟',
                        style: AppStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.amber.shade700,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            contributor.userName,
                            style: AppStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            '${contributor.taskCount} مهمة (${contributor.completedTasks} مكتملة)',
                            style: AppStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )),
            ] else ...[
              Text(
                'لا توجد بيانات مساهمين',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء التحليلات التنبؤية ومؤشرات الإنذار المبكر
  Widget _buildPredictiveAnalytics() {
    final predictiveAnalytics = _controller.predictiveAnalytics;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // مؤشرات الإنذار المبكر (إذا وجدت)
        if (predictiveAnalytics.earlyWarnings.isNotEmpty) ...[
          _buildSubSectionHeader(
            'تنبيهات تحتاج انتباه فوري',
            Icons.warning,
            Colors.red.shade600,
            'مؤشرات إنذار مبكر للمشاكل المحتملة',
          ),
          const SizedBox(height: 12),
          _buildEarlyWarningsCard(predictiveAnalytics.earlyWarnings),
          const SizedBox(height: 24),
        ],

        // القسم الفرعي الأول: تحليل المخاطر والفرص
        _buildSubSectionHeader(
          'تحليل المخاطر والفرص',
          Icons.analytics,
          Colors.orange.shade600,
          'المهام المعرضة للخطر والمتوقع اكتمالها قريباً',
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildTasksAtRiskCard(predictiveAnalytics.tasksAtRisk),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTasksLikelyToCompleteCard(predictiveAnalytics.tasksLikelyToComplete),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // القسم الفرعي الثاني: أنماط العمل والتنبؤات
        _buildSubSectionHeader(
          'أنماط العمل والتنبؤات',
          Icons.insights,
          Colors.purple.shade600,
          'تحليل أنماط الإنتاجية وملخص التنبؤات',
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildProductivityPatternsCard(predictiveAnalytics.productivityByDay),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildPredictiveSummaryCard(predictiveAnalytics.summary),
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة الإنذارات المبكرة
  Widget _buildEarlyWarningsCard(List<EarlyWarning> warnings) {
    return Card(
      color: Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning,
                  color: Colors.red.shade700,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'تنبيهات تحتاج انتباه فوري',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            ...warnings.map((warning) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: warning.severity == "عالي"
                  ? Colors.red.shade100
                  : Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: warning.severity == "عالي"
                    ? Colors.red.shade300
                    : Colors.orange.shade300,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    warning.severity == "عالي" ? Icons.error : Icons.warning_amber,
                    color: warning.severity == "عالي"
                      ? Colors.red.shade700
                      : Colors.orange.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      warning.message,
                      style: AppStyles.bodyMedium.copyWith(
                        color: warning.severity == "عالي"
                          ? Colors.red.shade800
                          : Colors.orange.shade800,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: warning.severity == "عالي"
                        ? Colors.red.shade200
                        : Colors.orange.shade200,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      warning.severity,
                      style: AppStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: warning.severity == "عالي"
                          ? Colors.red.shade800
                          : Colors.orange.shade800,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  /// بطاقة المهام المعرضة للخطر
  Widget _buildTasksAtRiskCard(List<TaskAtRisk> tasksAtRisk) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.dangerous,
                  color: Colors.red.shade600,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'مهام معرضة للخطر',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (tasksAtRisk.isNotEmpty) ...[
              ...tasksAtRisk.take(5).map((task) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: task.riskLevel == "عالي"
                    ? Colors.red.shade50
                    : task.riskLevel == "متوسط"
                      ? Colors.orange.shade50
                      : Colors.yellow.shade50,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: task.riskLevel == "عالي"
                      ? Colors.red.shade200
                      : task.riskLevel == "متوسط"
                        ? Colors.orange.shade200
                        : Colors.yellow.shade200,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            task.title,
                            style: AppStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: task.riskLevel == "عالي"
                              ? Colors.red.shade100
                              : task.riskLevel == "متوسط"
                                ? Colors.orange.shade100
                                : Colors.yellow.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            task.riskLevel,
                            style: AppStyles.bodySmall.copyWith(
                              fontWeight: FontWeight.bold,
                              color: task.riskLevel == "عالي"
                                ? Colors.red.shade700
                                : task.riskLevel == "متوسط"
                                  ? Colors.orange.shade700
                                  : Colors.yellow.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.schedule, size: 14, color: AppColors.textSecondary),
                        const SizedBox(width: 4),
                        Text(
                          '${task.daysRemaining.toStringAsFixed(1)} يوم متبقي',
                          style: AppStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(Icons.percent, size: 14, color: AppColors.textSecondary),
                        const SizedBox(width: 4),
                        Text(
                          '${task.completionPercentage}% مكتمل',
                          style: AppStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ] else ...[
              Text(
                'لا توجد مهام معرضة للخطر حالياً',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.success,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بطاقة المهام المتوقع اكتمالها
  Widget _buildTasksLikelyToCompleteCard(List<TaskLikelyToComplete> tasksLikelyToComplete) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: Colors.green.shade600,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'متوقع اكتمالها قريباً',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (tasksLikelyToComplete.isNotEmpty) ...[
              ...tasksLikelyToComplete.take(5).map((task) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      task.title,
                      style: AppStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.percent, size: 14, color: AppColors.textSecondary),
                        const SizedBox(width: 4),
                        Text(
                          '${task.completionPercentage}% مكتمل',
                          style: AppStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(Icons.schedule, size: 14, color: AppColors.textSecondary),
                        const SizedBox(width: 4),
                        Text(
                          'متوقع خلال ${((task.estimatedCompletion - DateTime.now().millisecondsSinceEpoch / 1000) / (24 * 60 * 60)).round()} أيام',
                          style: AppStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ] else ...[
              Text(
                'لا توجد مهام متوقع اكتمالها قريباً',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بطاقة أنماط الإنتاجية
  Widget _buildProductivityPatternsCard(List<ProductivityByDay> productivityByDay) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.insights,
                  color: AppColors.info,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'أنماط الإنتاجية',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.info,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (productivityByDay.isNotEmpty) ...[
              ...productivityByDay.map((day) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        day.dayName,
                        style: AppStyles.bodyMedium,
                      ),
                    ),
                    Container(
                      width: 60,
                      height: 6,
                      decoration: BoxDecoration(
                        color: AppColors.info.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: day.percentage / 100,
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.info,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${day.completedTasks}',
                      style: AppStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.info,
                      ),
                    ),
                  ],
                ),
              )),
            ] else ...[
              Text(
                'لا توجد بيانات كافية لأنماط الإنتاجية',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بطاقة ملخص التنبؤات
  Widget _buildPredictiveSummaryCard(PredictiveSummary summary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.summarize,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'ملخص التنبؤات',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildSummaryItem(
              'مهام معرضة للخطر',
              '${summary.totalTasksAtRisk}',
              Icons.warning,
              summary.totalTasksAtRisk > 0 ? AppColors.error : AppColors.success,
            ),
            const SizedBox(height: 8),

            _buildSummaryItem(
              'مهام عالية الخطورة',
              '${summary.highRiskTasks}',
              Icons.error,
              summary.highRiskTasks > 0 ? AppColors.error : AppColors.success,
            ),
            const SizedBox(height: 8),

            _buildSummaryItem(
              'متوقع اكتمالها',
              '${summary.tasksExpectedToComplete}',
              Icons.check_circle,
              AppColors.success,
            ),
            const SizedBox(height: 8),

            _buildSummaryItem(
              'اليوم الأكثر إنتاجية',
              summary.mostProductiveDay.isNotEmpty ? summary.mostProductiveDay : 'غير محدد',
              Icons.star,
              AppColors.warning,
            ),

            if (summary.totalWarnings > 0) ...[
              const SizedBox(height: 8),
              _buildSummaryItem(
                'تنبيهات نشطة',
                '${summary.totalWarnings}',
                Icons.notifications_active,
                AppColors.warning,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// عنصر ملخص فردي
  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: AppStyles.bodyMedium,
          ),
        ),
        Text(
          value,
          style: AppStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// بناء تحليلات أنواع المهام
  Widget _buildTaskTypesAnalytics() {
    final taskTypesAnalytics = _controller.taskTypesAnalytics;
    final taskTypesTrends = _controller.taskTypesTrends;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // القسم الفرعي الأول: تحليل الاستخدام والكفاءة
        _buildSubSectionHeader(
          'تحليل الاستخدام والكفاءة',
          Icons.bar_chart,
          AppColors.info,
          'الأنواع الأكثر استخداماً والأكثر كفاءة في الإنجاز',
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildMostUsedTypesCard(taskTypesAnalytics.mostUsedTypes),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMostEfficientTypesCard(taskTypesAnalytics.mostEfficientTypes),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // القسم الفرعي الثاني: الاتجاهات والنمو
        _buildSubSectionHeader(
          'اتجاهات الأداء والنمو',
          Icons.trending_up,
          AppColors.success,
          'تحليل أوقات الإنجاز واتجاهات النمو لكل نوع',
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildCompletionTimesCard(taskTypesTrends.completionTimeByType),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildGrowthAnalysisCard(taskTypesTrends.growthAnalysis),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // القسم الفرعي الثالث: الملخص الشامل
        _buildSubSectionHeader(
          'الملخص الشامل',
          Icons.summarize,
          AppColors.primary,
          'نظرة عامة على جميع مؤشرات أنواع المهام',
        ),
        const SizedBox(height: 12),

        _buildTaskTypesSummaryCard(taskTypesAnalytics.summary, taskTypesTrends.summary),
      ],
    );
  }

  /// بطاقة الأنواع الأكثر استخداماً
  Widget _buildMostUsedTypesCard(List<TaskTypePerformance> mostUsedTypes) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: AppColors.info,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'الأنواع الأكثر استخداماً',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.info,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (mostUsedTypes.isNotEmpty) ...[
              ...mostUsedTypes.take(5).map((type) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    // أيقونة النوع
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: type.typeColor != null
                          ? Color(int.parse(type.typeColor!.replaceFirst('#', '0xFF')))
                          : AppColors.info.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        _getIconFromString(type.typeIcon),
                        size: 16,
                        color: AppColors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            type.typeName,
                            style: AppStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            '${type.totalTasks} مهمة (${type.completionRate.toStringAsFixed(1)}% مكتملة)',
                            style: AppStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.info.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${type.totalTasks}',
                        style: AppStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.info,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ] else ...[
              Text(
                'لا توجد بيانات كافية',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بطاقة الأنواع الأكثر كفاءة
  Widget _buildMostEfficientTypesCard(List<TaskTypePerformance> mostEfficientTypes) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.star,
                  color: AppColors.warning,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'الأنواع الأكثر كفاءة',
                    style: AppStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.warning,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(
                    Icons.info_outline,
                    color: AppColors.warning,
                    size: 20,
                  ),
                  onPressed: () => _showEfficiencyExplanation(),
                  tooltip: 'شرح قانون الكفاءة',
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (mostEfficientTypes.isNotEmpty) ...[
              ...mostEfficientTypes.take(5).map((type) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    // أيقونة النوع
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: type.typeColor != null
                          ? Color(int.parse(type.typeColor!.replaceFirst('#', '0xFF')))
                          : AppColors.warning.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        _getIconFromString(type.typeIcon),
                        size: 16,
                        color: AppColors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            type.typeName,
                            style: AppStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'كفاءة: ${type.efficiencyScore.toStringAsFixed(1)}% | إنجاز: ${type.completionRate.toStringAsFixed(1)}%',
                            style: AppStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.warning.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${type.efficiencyScore.toStringAsFixed(0)}%',
                        style: AppStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.warning,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ] else ...[
              Text(
                'لا توجد بيانات كافية',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// دالة مساعدة لتحويل النص إلى أيقونة
  IconData _getIconFromString(String? iconString) {
    switch (iconString?.toLowerCase()) {
      case 'bug_report':
        return Icons.bug_report;
      case 'feature':
        return Icons.new_releases;
      case 'task':
        return Icons.task;
      case 'meeting':
        return Icons.meeting_room;
      case 'document':
        return Icons.description;
      case 'support':
        return Icons.support;
      default:
        return Icons.work;
    }
  }

  /// بطاقة أوقات الإنجاز
  Widget _buildCompletionTimesCard(List<CompletionTimeByType> completionTimes) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: AppColors.success,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'أوقات الإنجاز',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (completionTimes.isNotEmpty) ...[
              ...completionTimes.take(5).map((time) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        time.typeName,
                        style: AppStyles.bodyMedium,
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${time.averageCompletionDays.toStringAsFixed(1)} يوم',
                          style: AppStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.success,
                          ),
                        ),
                        Text(
                          '${time.taskCount} مهمة',
                          style: AppStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ] else ...[
              Text(
                'لا توجد بيانات كافية',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بطاقة تحليل النمو
  Widget _buildGrowthAnalysisCard(List<GrowthAnalysis> growthAnalysis) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'تحليل النمو',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (growthAnalysis.isNotEmpty) ...[
              ...growthAnalysis.take(5).map((growth) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: growth.growthRate >= 0
                    ? AppColors.success.withValues(alpha: 0.1)
                    : AppColors.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: growth.growthRate >= 0
                      ? AppColors.success.withValues(alpha: 0.3)
                      : AppColors.error.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      growth.growthRate >= 0
                        ? Icons.trending_up
                        : Icons.trending_down,
                      color: growth.growthRate >= 0
                        ? AppColors.success
                        : AppColors.error,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            growth.typeName,
                            style: AppStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'هذا الأسبوع: ${growth.recentWeekCount} | السابق: ${growth.previousWeekCount}',
                            style: AppStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: growth.growthRate >= 0
                          ? AppColors.success.withValues(alpha: 0.2)
                          : AppColors.error.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${growth.growthRate >= 0 ? '+' : ''}${growth.growthRate.toStringAsFixed(0)}%',
                        style: AppStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                          color: growth.growthRate >= 0
                            ? AppColors.success
                            : AppColors.error,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ] else ...[
              Text(
                'لا توجد بيانات كافية',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بطاقة ملخص أنواع المهام
  Widget _buildTaskTypesSummaryCard(TaskTypesSummary analyticsSummary, TrendsSummary trendsSummary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.summarize,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'ملخص تحليلات أنواع المهام',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      _buildSummaryMetric(
                        'إجمالي الأنواع',
                        '${analyticsSummary.totalTypes}',
                        Icons.category,
                        AppColors.info,
                      ),
                      const SizedBox(height: 12),
                      _buildSummaryMetric(
                        'إجمالي المهام',
                        '${analyticsSummary.totalTasks}',
                        Icons.task,
                        AppColors.primary,
                      ),
                      const SizedBox(height: 12),
                      _buildSummaryMetric(
                        'متوسط المهام/نوع',
                        analyticsSummary.averageTasksPerType.toStringAsFixed(1),
                        Icons.analytics,
                        AppColors.accent,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    children: [
                      _buildSummaryMetric(
                        'الأفضل أداءً',
                        analyticsSummary.bestPerformingType.isNotEmpty
                          ? analyticsSummary.bestPerformingType
                          : 'غير محدد',
                        Icons.star,
                        AppColors.warning,
                      ),
                      const SizedBox(height: 12),
                      _buildSummaryMetric(
                        'الأكثر استخداماً',
                        analyticsSummary.mostUsedType.isNotEmpty
                          ? analyticsSummary.mostUsedType
                          : 'غير محدد',
                        Icons.trending_up,
                        AppColors.info,
                      ),
                      const SizedBox(height: 12),
                      _buildSummaryMetric(
                        'الأسرع إنجازاً',
                        trendsSummary.fastestType.isNotEmpty
                          ? trendsSummary.fastestType
                          : 'غير محدد',
                        Icons.speed,
                        AppColors.success,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// مؤشر ملخص فردي
  Widget _buildSummaryMetric(String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: AppStyles.bodySmall,
          ),
        ),
        Text(
          value,
          style: AppStyles.bodySmall.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// بناء فاصل بصري بين الأقسام
  Widget _buildSectionDivider() {
    return Container(
      height: 1,
      margin: const EdgeInsets.symmetric(horizontal: 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.transparent,
            AppColors.textSecondary.withValues(alpha: 0.3),
            AppColors.transparent,
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
    );
  }

  /// بناء رأس قسم التحليلات
  Widget _buildAnalyticsHeader(String title, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: AppStyles.headingMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              'تحليلات متقدمة',
              style: AppStyles.bodySmall.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض شرح قانون الكفاءة
  void _showEfficiencyExplanation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.calculate,
                color: AppColors.warning,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'قانون حساب الكفاءة',
                style: AppStyles.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'يتم حساب نقاط الكفاءة بناءً على 4 معايير رئيسية:',
                  style: AppStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),

                _buildEfficiencyFactor(
                  '1. معدل الإنجاز الكامل',
                  '40% من النقاط',
                  'عدد المهام المكتملة ÷ إجمالي المهام',
                  AppColors.success,
                  Icons.check_circle,
                ),

                _buildEfficiencyFactor(
                  '2. متوسط التقدم العام',
                  '30% من النقاط',
                  'متوسط نسبة الإنجاز لجميع المهام',
                  AppColors.info,
                  Icons.trending_up,
                ),

                _buildEfficiencyFactor(
                  '3. معامل الجودة',
                  '20% من النقاط',
                  'عدم وجود مهام متأخرة (الالتزام بالمواعيد)',
                  AppColors.warning,
                  Icons.schedule,
                ),

                _buildEfficiencyFactor(
                  '4. معامل الاستقرار',
                  '10% من النقاط',
                  'حجم العينة (كلما زادت المهام زادت الموثوقية)',
                  AppColors.primary,
                  Icons.data_usage,
                ),

                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.warning.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.lightbulb,
                            color: AppColors.warning,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'مثال عملي:',
                            style: AppStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.warning,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'نوع مهمة لديه 10 مهام، 8 منها مكتملة، بدون تأخير، ومتوسط التقدم 85%',
                        style: AppStyles.bodySmall,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'الكفاءة = (8÷10)×40 + (85÷100)×30 + (1)×20 + (5)×10 = 87%',
                        style: AppStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.warning,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'فهمت',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.warning,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// بناء عامل كفاءة فردي
  Widget _buildEfficiencyFactor(
    String title,
    String weight,
    String description,
    Color color,
    IconData icon,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: AppStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: color,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        weight,
                        style: AppStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

 // / بناء حالة التحميل
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل بيانات لوحة التحكم...'),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _controller.error,
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _controller.loadDashboardData(forceRefresh: true),
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء زر الإجراء العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () => _controller.refreshDashboard(),
      backgroundColor: Theme.of(context).primaryColor,
      tooltip: 'تحديث البيانات',
      child: Icon(
        Icons.refresh,
        color: Theme.of(context).colorScheme.onPrimary,
      ),
    );
  }








}
